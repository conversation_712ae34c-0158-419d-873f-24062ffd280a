diff --git a/crypto-indicator-web/src/components/stock/StockMainContent.tsx b/crypto-indicator-web/src/components/stock/StockMainContent.tsx
index 783a644..ced825e 100644
--- a/crypto-indicator-web/src/components/stock/StockMainContent.tsx
+++ b/crypto-indicator-web/src/components/stock/StockMainContent.tsx
@@ -1,11 +1,19 @@
 import React from 'react';
 
-import { StockFilterControls } from './StockFilterControls';
-import { StockTable } from './StockTable';
+import { STOCK_CONFIG } from '../../types/genericTable';
+import { GenericResponsiveTableContainer } from '../table/GenericResponsiveTableContainer';
 
 import type { StockStatisticsDto } from '../../generated';
 import type { StockFilterConfig, StockSortColumn } from '../../types/table';
 
+// Dummy formatDate function for stocks (can be enhanced later)
+const formatDate = (date?: string): string => {
+  if (date === null || date === undefined || date === '') {
+    return '';
+  }
+  return new Date(date).toLocaleDateString();
+};
+
 interface StockMainContentProps {
   data: StockStatisticsDto[];
   totalCount: number;
@@ -30,40 +38,31 @@ export const StockMainContent: React.FC<StockMainContentProps> = ({
   filterConfig,
   hasActiveFilters,
   onSignalClick,
-  onRefresh,
+  onRefresh: _onRefresh,
   onSort,
   getSortDirection,
   onSymbolSearchChange,
   onSignalChange,
   onClearFilters,
 }) => {
+
+
   return (
     <div className="stock-main-content">
-      <div className="content-header">
-        <h2>Stock Statistics</h2>
-        <p>
-          Showing {filteredCount} of {totalCount} stocks
-          {hasActiveFilters && ' (filtered)'}
-        </p>
-      </div>
-
-      {/* Search and Filter Controls */}
-      <StockFilterControls
+      <GenericResponsiveTableContainer
+        assetConfig={STOCK_CONFIG}
+        data={data}
+        onSignalClick={onSignalClick}
+        formatDate={formatDate}
+        onSort={onSort}
+        getSortDirection={getSortDirection}
         filterConfig={filterConfig}
-        hasActiveFilters={hasActiveFilters}
-        loading={loading}
         onSymbolSearchChange={onSymbolSearchChange}
         onSignalChange={onSignalChange}
         onClearFilters={onClearFilters}
-        onRefresh={onRefresh}
-      />
-
-      {/* Stock Table */}
-      <StockTable
-        data={data}
-        onSort={onSort}
-        getSortDirection={getSortDirection}
-        onSignalClick={onSignalClick}
+        hasActiveFilters={hasActiveFilters}
+        filteredCount={filteredCount}
+        totalCount={totalCount}
       />
 
       {data.length === 0 && !loading && (
diff --git a/crypto-indicator-web/src/components/table/GenericDesktopLayout.tsx b/crypto-indicator-web/src/components/table/GenericDesktopLayout.tsx
new file mode 100644
index 0000000..72492fa
--- /dev/null
+++ b/crypto-indicator-web/src/components/table/GenericDesktopLayout.tsx
@@ -0,0 +1,77 @@
+import React from "react";
+
+import { CSS_CLASSES } from "../../constants/app";
+
+import { GenericSortableTableHeader } from "./GenericSortableTableHeader";
+import { GenericTableFilters } from "./GenericTableFilters";
+import { GenericTableRow } from "./GenericTableRow";
+
+import type { GenericTableProps } from "../../types/genericTable";
+
+interface GenericDesktopLayoutProps extends GenericTableProps {
+  showFilters: boolean;
+  onToggleFilters: () => void;
+  onCloseFilters: () => void;
+  filteredCount?: number;
+  totalCount?: number;
+}
+
+export const GenericDesktopLayout: React.FC<GenericDesktopLayoutProps> = ({
+  assetConfig,
+  data,
+  btcStatistics,
+  onSignalClick,
+  formatDate,
+  findBtcDataForSymbol,
+  onSort,
+  getSortDirection,
+  filterConfig,
+  onSymbolSearchChange,
+  onUsdSignalChange,
+  onBtcSignalChange,
+  onSignalChange,
+  onClearFilters,
+  hasActiveFilters,
+  filteredCount = data.length,
+  totalCount = data.length,
+}) => {
+  return (
+    <div className="desktop-layout">
+      <GenericTableFilters
+        assetConfig={assetConfig}
+        filterConfig={filterConfig}
+        onSymbolSearchChange={onSymbolSearchChange}
+        onUsdSignalChange={onUsdSignalChange}
+        onBtcSignalChange={onBtcSignalChange}
+        onSignalChange={onSignalChange}
+        onClearFilters={onClearFilters}
+        hasActiveFilters={hasActiveFilters}
+        filteredCount={filteredCount}
+        totalCount={totalCount}
+      />
+
+      <div className={CSS_CLASSES.TABLE_CONTAINER}>
+        <table className={CSS_CLASSES.TABLE}>
+          <GenericSortableTableHeader
+            assetConfig={assetConfig}
+            onSort={onSort}
+            getSortDirection={getSortDirection}
+          />
+          <tbody>
+            {data.map((item) => (
+              <GenericTableRow
+                key={item.symbol}
+                assetConfig={assetConfig}
+                data={item}
+                btcStatistics={btcStatistics}
+                onSignalClick={onSignalClick}
+                formatDate={formatDate}
+                findBtcDataForSymbol={findBtcDataForSymbol}
+              />
+            ))}
+          </tbody>
+        </table>
+      </div>
+    </div>
+  );
+};
diff --git a/crypto-indicator-web/src/components/table/GenericMobileLayout.tsx b/crypto-indicator-web/src/components/table/GenericMobileLayout.tsx
new file mode 100644
index 0000000..619b96c
--- /dev/null
+++ b/crypto-indicator-web/src/components/table/GenericMobileLayout.tsx
@@ -0,0 +1,19 @@
+import React from "react";
+
+import { GenericDesktopLayout } from "./GenericDesktopLayout";
+
+import type { GenericTableProps } from "../../types/genericTable";
+
+interface GenericMobileLayoutProps extends GenericTableProps {
+  showFilters: boolean;
+  onToggleFilters: () => void;
+  onCloseFilters: () => void;
+  filteredCount?: number;
+  totalCount?: number;
+}
+
+// For now, use desktop layout as fallback
+// TODO: Implement proper mobile card layout
+export const GenericMobileLayout: React.FC<GenericMobileLayoutProps> = (props) => {
+  return <GenericDesktopLayout {...props} />;
+};
diff --git a/crypto-indicator-web/src/components/table/GenericResponsiveTableContainer.tsx b/crypto-indicator-web/src/components/table/GenericResponsiveTableContainer.tsx
new file mode 100644
index 0000000..e049195
--- /dev/null
+++ b/crypto-indicator-web/src/components/table/GenericResponsiveTableContainer.tsx
@@ -0,0 +1,68 @@
+import React, { useState } from 'react';
+
+import { useResponsiveLayout } from '../../hooks/useResponsiveLayout';
+
+import { GenericDesktopLayout } from './GenericDesktopLayout';
+import { GenericMobileLayout } from './GenericMobileLayout';
+import { GenericTabletLayout } from './GenericTabletLayout';
+
+import type { GenericTableProps } from '../../types/genericTable';
+
+// Component-specific styles
+import '../../styles/layout/responsive-layout.css';
+
+export const GenericResponsiveTableContainer: React.FC<GenericTableProps> = (props) => {
+  const [showFilters, setShowFilters] = useState(false);
+  const { isMobile, isTablet, isDesktop } = useResponsiveLayout();
+
+  const toggleFilters = () => {
+    setShowFilters(!showFilters);
+  };
+
+  const closeFilters = () => {
+    setShowFilters(false);
+  };
+
+  if (isMobile) {
+    return (
+      <GenericMobileLayout
+        {...props}
+        showFilters={showFilters}
+        onToggleFilters={toggleFilters}
+        onCloseFilters={closeFilters}
+      />
+    );
+  }
+
+  if (isTablet) {
+    return (
+      <GenericTabletLayout
+        {...props}
+        showFilters={showFilters}
+        onToggleFilters={toggleFilters}
+        onCloseFilters={closeFilters}
+      />
+    );
+  }
+
+  if (isDesktop) {
+    return (
+      <GenericDesktopLayout
+        {...props}
+        showFilters={showFilters}
+        onToggleFilters={toggleFilters}
+        onCloseFilters={closeFilters}
+      />
+    );
+  }
+
+  // Fallback to desktop layout
+  return (
+    <GenericDesktopLayout
+      {...props}
+      showFilters={showFilters}
+      onToggleFilters={toggleFilters}
+      onCloseFilters={closeFilters}
+    />
+  );
+};
diff --git a/crypto-indicator-web/src/components/table/GenericSortableTableHeader.tsx b/crypto-indicator-web/src/components/table/GenericSortableTableHeader.tsx
new file mode 100644
index 0000000..b8b97d0
--- /dev/null
+++ b/crypto-indicator-web/src/components/table/GenericSortableTableHeader.tsx
@@ -0,0 +1,89 @@
+import React from 'react';
+
+import type { AssetConfig, GenericSortColumn } from '../../types/genericTable';
+import type { SortDirection } from '../../types/table';
+
+interface GenericSortableTableHeaderProps {
+  assetConfig: AssetConfig;
+  onSort: (column: GenericSortColumn) => void;
+  getSortDirection: (column: GenericSortColumn) => SortDirection;
+}
+
+const getSortIcon = (direction: SortDirection): string => {
+  switch (direction) {
+    case 'asc': {
+      return '▲';
+    }
+    case 'desc': {
+      return '▼';
+    }
+    default: {
+      return '↕';
+    }
+  }
+};
+
+const getAriaSort = (direction: SortDirection | null): 'ascending' | 'descending' | 'none' => {
+  if (direction === 'asc') {return 'ascending';}
+  if (direction === 'desc') {return 'descending';}
+  return 'none';
+};
+
+export const GenericSortableTableHeader: React.FC<GenericSortableTableHeaderProps> = ({
+  assetConfig,
+  onSort,
+  getSortDirection,
+}) => {
+  const createSortableHeader = (
+    headerText: string,
+    column: GenericSortColumn,
+    className = 'sortable-header'
+  ) => {
+    const direction = getSortDirection(column);
+    const sortIcon = getSortIcon(direction);
+    const ariaSort = getAriaSort(direction);
+
+    return (
+      <th key={column} className={className}>
+        <button
+          type="button"
+          onClick={() => { onSort(column); }}
+          className="header-button"
+          aria-sort={ariaSort}
+        >
+          <span className="header-text">{headerText}</span>
+          <span className="sort-icon">{sortIcon}</span>
+        </button>
+      </th>
+    );
+  };
+
+  return (
+    <thead>
+      <tr>
+        {/* Asset column (always present) */}
+        {createSortableHeader(assetConfig.headers.asset, 'symbol')}
+        
+        {/* Price column (always present) */}
+        {createSortableHeader(assetConfig.headers.price, assetConfig.type === 'crypto' ? 'usdPrice' : 'price')}
+        
+        {/* Market Cap column (crypto only) */}
+        {assetConfig.showMarketCap && createSortableHeader(assetConfig.headers.marketCap!, 'marketCap')}
+        
+        {/* Volume column (stock only) */}
+        {assetConfig.showVolume && createSortableHeader(assetConfig.headers.volume!, 'volume')}
+        
+        {/* Signal column (always present) */}
+        {createSortableHeader(assetConfig.headers.signal, assetConfig.type === 'crypto' ? 'usdSignal' : 'signal')}
+        
+        {/* BTC columns (crypto only) */}
+        {assetConfig.showBtcColumns && (
+          <>
+            {createSortableHeader(assetConfig.headers.btcPrice!, 'btcPrice')}
+            {createSortableHeader(assetConfig.headers.btcSignal!, 'btcSignal')}
+          </>
+        )}
+      </tr>
+    </thead>
+  );
+};
diff --git a/crypto-indicator-web/src/components/table/GenericTableFilters.tsx b/crypto-indicator-web/src/components/table/GenericTableFilters.tsx
new file mode 100644
index 0000000..c6fdb95
--- /dev/null
+++ b/crypto-indicator-web/src/components/table/GenericTableFilters.tsx
@@ -0,0 +1,123 @@
+import React from 'react';
+
+import type { AssetConfig, GenericFilterConfig } from '../../types/genericTable';
+import type { FilterConfig, StockFilterConfig } from '../../types/table';
+
+// Component-specific styles
+import '../../styles/components/filters.css';
+
+interface GenericTableFiltersProps {
+  assetConfig: AssetConfig;
+  filterConfig: GenericFilterConfig;
+  onSymbolSearchChange: (search: string) => void;
+  onUsdSignalChange?: (signal: FilterConfig['usdSignal']) => void;
+  onBtcSignalChange?: (signal: FilterConfig['btcSignal']) => void;
+  onSignalChange?: (signal: StockFilterConfig['signal']) => void;
+  onClearFilters: () => void;
+  hasActiveFilters: boolean;
+  filteredCount: number;
+  totalCount: number;
+}
+
+// eslint-disable-next-line max-lines-per-function
+export const GenericTableFilters: React.FC<GenericTableFiltersProps> = ({
+  assetConfig,
+  filterConfig,
+  onSymbolSearchChange,
+  onUsdSignalChange,
+  onBtcSignalChange,
+  onSignalChange,
+  onClearFilters,
+  hasActiveFilters,
+  filteredCount,
+  totalCount,
+}) => {
+  const assetName = assetConfig.type === 'crypto' ? 'cryptocurrencies' : 'stocks';
+  const searchPlaceholder = `Search ${assetName}...`;
+
+  return (
+    <div className="table-filters">
+      <div className="filters-row">
+        <div className="filter-group">
+          <label htmlFor="symbol-search">Search:</label>
+          <input
+            id="symbol-search"
+            type="text"
+            placeholder={searchPlaceholder}
+            value={filterConfig.symbolSearch}
+            onChange={(e) => { onSymbolSearchChange(e.target.value); }}
+            className="filter-input"
+          />
+        </div>
+
+        {/* Crypto-specific filters */}
+        {assetConfig.type === 'crypto' && onUsdSignalChange && onBtcSignalChange && (
+          <>
+            <div className="filter-group">
+              <label htmlFor="usd-signal">USD Signal:</label>
+              <select
+                id="usd-signal"
+                value={(filterConfig as FilterConfig).usdSignal}
+                onChange={(e) => { onUsdSignalChange(e.target.value as FilterConfig['usdSignal']); }}
+                className="filter-select"
+              >
+                <option value="all">All</option>
+                <option value="gold">Gold (Bullish)</option>
+                <option value="blue">Blue (Bearish)</option>
+                <option value="gray">Gray (Neutral)</option>
+              </select>
+            </div>
+
+            <div className="filter-group">
+              <label htmlFor="btc-signal">BTC Signal:</label>
+              <select
+                id="btc-signal"
+                value={(filterConfig as FilterConfig).btcSignal}
+                onChange={(e) => { onBtcSignalChange(e.target.value as FilterConfig['btcSignal']); }}
+                className="filter-select"
+              >
+                <option value="all">All</option>
+                <option value="gold">Gold (Bullish)</option>
+                <option value="blue">Blue (Bearish)</option>
+                <option value="gray">Gray (Neutral)</option>
+              </select>
+            </div>
+          </>
+        )}
+
+        {/* Stock-specific filters */}
+        {assetConfig.type === 'stock' && onSignalChange && (
+          <div className="filter-group">
+            <label htmlFor="signal">Signal:</label>
+            <select
+              id="signal"
+              value={(filterConfig as StockFilterConfig).signal}
+              onChange={(e) => { onSignalChange(e.target.value as StockFilterConfig['signal']); }}
+              className="filter-select"
+            >
+              <option value="all">All Signals</option>
+              <option value="gold">Bullish (Gold)</option>
+              <option value="blue">Bearish (Blue)</option>
+              <option value="gray">Neutral (Gray)</option>
+            </select>
+          </div>
+        )}
+
+        {hasActiveFilters && (
+          <button
+            type="button"
+            onClick={onClearFilters}
+            className="clear-filters-btn"
+          >
+            Clear Filters
+          </button>
+        )}
+      </div>
+
+      <div className="filter-summary">
+        Showing {filteredCount} of {totalCount} {assetName}
+        {hasActiveFilters && ' (filtered)'}
+      </div>
+    </div>
+  );
+};
diff --git a/crypto-indicator-web/src/components/table/GenericTableRow.tsx b/crypto-indicator-web/src/components/table/GenericTableRow.tsx
new file mode 100644
index 0000000..b3304e1
--- /dev/null
+++ b/crypto-indicator-web/src/components/table/GenericTableRow.tsx
@@ -0,0 +1,149 @@
+import React from "react";
+
+import { CSS_CLASSES, CURRENCIES } from "../../constants/app";
+import { isCryptoData } from "../../types/genericTable";
+import { formatters, navigation } from "../../utils/formatters";
+import { formatStockPrice, formatStockVolume } from "../../utils/stockDataProcessors";
+import { SignalBadge } from "../signals/SignalBadge";
+
+import type { CryptoCurrencyStatisticsDto, IndicatorValueDto } from "../../generated";
+import type { AssetConfig, AssetData } from "../../types/genericTable";
+
+interface GenericTableRowProps {
+  assetConfig: AssetConfig;
+  data: AssetData;
+  btcStatistics?: CryptoCurrencyStatisticsDto[];
+  onSignalClick: (symbol: string, currency: string) => void;
+  formatDate: (date?: string) => string;
+  findBtcDataForSymbol?: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
+}
+
+// eslint-disable-next-line max-lines-per-function
+export const GenericTableRow: React.FC<GenericTableRowProps> = ({
+  assetConfig,
+  data,
+  btcStatistics,
+  onSignalClick,
+  formatDate,
+  findBtcDataForSymbol,
+}) => {
+  const latestData = data.indicatorValues.find(Boolean);
+  
+  // Get BTC data for crypto assets
+  const btcData = assetConfig.type === 'crypto' && btcStatistics && findBtcDataForSymbol 
+    ? findBtcDataForSymbol(btcStatistics, data.symbol)
+    : undefined;
+
+  // Create tooltip text with last update information
+  const primaryTooltip = `Click to view chart${
+    (latestData?.timestamp !== null && latestData?.timestamp !== undefined) 
+      ? ` • Last update: ${formatDate(latestData.timestamp)}` 
+      : ""
+  }`;
+  
+  const btcTooltip = `Click to view chart${
+    (btcData?.timestamp !== null && btcData?.timestamp !== undefined) 
+      ? ` • Last update: ${formatDate(btcData.timestamp)}` 
+      : ""
+  }`;
+
+  const handleSymbolClick = () => {
+    if (assetConfig.type === 'crypto' && isCryptoData(data)) {
+      navigation.openCoinMarketCap(data.mapping?.slug);
+    }
+    // For stocks, we could add navigation to a stock info site
+  };
+
+  const getSymbolIcon = () => {
+    if (assetConfig.type === 'crypto') {
+      return '🔗';
+    }
+    return '📈';
+  };
+
+  const getSymbolTitle = () => {
+    if (assetConfig.type === 'crypto') {
+      return `🔗 Click to view ${data.symbol} on CoinMarketCap (opens in new tab)`;
+    }
+    return `📈 ${data.symbol} stock information`;
+  };
+
+  return (
+    <tr key={data.symbol}>
+      {/* Asset Symbol Column */}
+      <td data-label={assetConfig.headers.asset}>
+        <div className={CSS_CLASSES.SYMBOL_CELL}>
+          <button
+            className="clickable-symbol"
+            onClick={handleSymbolClick}
+            type="button"
+            title={getSymbolTitle()}
+            aria-label={`View ${data.symbol} information`}
+          >
+            {getSymbolIcon()}
+            <strong>{data.symbol}</strong>
+          </button>
+        </div>
+      </td>
+
+      {/* Price Column */}
+      <td data-label={assetConfig.headers.price}>
+        <strong>
+          {assetConfig.type === 'crypto' 
+            ? formatters.formatCurrency(latestData?.close, CURRENCIES.USD)
+            : formatStockPrice(latestData?.close, data.conversionCurrency)
+          }
+        </strong>
+      </td>
+
+      {/* Market Cap Column (Crypto only) */}
+      {assetConfig.showMarketCap && (
+        <td data-label={assetConfig.headers.marketCap}>
+          <strong>
+            {latestData?.marketCap 
+              ? formatters.formatMarketCap(latestData.marketCap)
+              : "-"
+            }
+          </strong>
+        </td>
+      )}
+
+      {/* Volume Column (Stock only) */}
+      {assetConfig.showVolume && (
+        <td data-label={assetConfig.headers.volume}>
+          <strong>{formatStockVolume(latestData?.volume)}</strong>
+        </td>
+      )}
+
+      {/* Primary Signal Column */}
+      <td data-label={assetConfig.headers.signal}>
+        <SignalBadge
+          signal={latestData?.color}
+          onClick={() => { onSignalClick(data.symbol, data.conversionCurrency); }}
+          tooltip={primaryTooltip}
+        />
+      </td>
+
+      {/* BTC Columns (Crypto only) */}
+      {assetConfig.showBtcColumns && (
+        <>
+          <td data-label={assetConfig.headers.btcPrice}>
+            <strong>
+              {btcData?.close 
+                ? formatters.formatCurrency(btcData.close, CURRENCIES.BTC)
+                : "-"
+              }
+            </strong>
+          </td>
+          <td data-label={assetConfig.headers.btcSignal}>
+            <SignalBadge
+              signal={btcData?.color}
+              onClick={() => { onSignalClick(data.symbol, CURRENCIES.BTC); }}
+              tooltip={btcTooltip}
+            />
+          </td>
+        </>
+      )}
+    </tr>
+  );
+};
diff --git a/crypto-indicator-web/src/components/table/GenericTabletLayout.tsx b/crypto-indicator-web/src/components/table/GenericTabletLayout.tsx
new file mode 100644
index 0000000..e39a062
--- /dev/null
+++ b/crypto-indicator-web/src/components/table/GenericTabletLayout.tsx
@@ -0,0 +1,19 @@
+import React from "react";
+
+import { GenericDesktopLayout } from "./GenericDesktopLayout";
+
+import type { GenericTableProps } from "../../types/genericTable";
+
+interface GenericTabletLayoutProps extends GenericTableProps {
+  showFilters: boolean;
+  onToggleFilters: () => void;
+  onCloseFilters: () => void;
+  filteredCount?: number;
+  totalCount?: number;
+}
+
+// For now, use desktop layout as fallback
+// TODO: Implement proper tablet layout
+export const GenericTabletLayout: React.FC<GenericTabletLayoutProps> = (props) => {
+  return <GenericDesktopLayout {...props} />;
+};
diff --git a/crypto-indicator-web/src/hooks/useChart.ts b/crypto-indicator-web/src/hooks/useChart.ts
index d6d7a9c..4fe8dc0 100644
--- a/crypto-indicator-web/src/hooks/useChart.ts
+++ b/crypto-indicator-web/src/hooks/useChart.ts
@@ -34,6 +34,7 @@ export const useChart = (data: ChartDataSource): UseChartReturn => {
       chartRef.current = chart;
 
       // Setup series with data
+      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
       chartHelpers.setupChartSeries(chart, data);
     }, CHART_CONSTANTS.CHART_INIT_DELAY_MS);
 
diff --git a/crypto-indicator-web/src/types/genericTable.ts b/crypto-indicator-web/src/types/genericTable.ts
new file mode 100644
index 0000000..b08ede1
--- /dev/null
+++ b/crypto-indicator-web/src/types/genericTable.ts
@@ -0,0 +1,89 @@
+import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from '../generated';
+import type { FilterConfig, SortColumn, SortDirection, StockFilterConfig, StockSortColumn } from './table';
+
+// Generic data types
+export type AssetData = CryptoCurrencyStatisticsDto | StockStatisticsDto;
+export type AssetType = 'crypto' | 'stock';
+
+// Generic sort and filter types
+export type GenericSortColumn = SortColumn | StockSortColumn;
+export type GenericFilterConfig = FilterConfig | StockFilterConfig;
+
+// Asset-specific configurations
+export interface AssetConfig {
+  type: AssetType;
+  headers: {
+    asset: string; // "Cryptocurrency" or "Stock"
+    price: string; // "USD Price" or "Price"
+    volume?: string; // "Volume" (only for stocks)
+    marketCap?: string; // "Market Cap" (only for crypto)
+    signal: string; // "USD Signal" or "Signal"
+    btcPrice?: string; // "BTC Price" (only for crypto)
+    btcSignal?: string; // "BTC Signal" (only for crypto)
+  };
+  showBtcColumns: boolean;
+  showMarketCap: boolean;
+  showVolume: boolean;
+}
+
+// Crypto configuration
+export const CRYPTO_CONFIG: AssetConfig = {
+  type: 'crypto',
+  headers: {
+    asset: 'Cryptocurrency',
+    price: 'USD Price',
+    marketCap: 'Market Cap',
+    signal: 'USD Signal',
+    btcPrice: 'BTC Price',
+    btcSignal: 'BTC Signal',
+  },
+  showBtcColumns: true,
+  showMarketCap: true,
+  showVolume: false,
+};
+
+// Stock configuration
+export const STOCK_CONFIG: AssetConfig = {
+  type: 'stock',
+  headers: {
+    asset: 'Stock',
+    price: 'Price',
+    volume: 'Volume',
+    signal: 'Signal',
+  },
+  showBtcColumns: false,
+  showMarketCap: false,
+  showVolume: true,
+};
+
+// Generic table props interface
+export interface GenericTableProps {
+  assetConfig: AssetConfig;
+  data: AssetData[];
+  btcStatistics?: CryptoCurrencyStatisticsDto[]; // Only needed for crypto
+  onSignalClick: (symbol: string, currency: string) => void;
+  formatDate: (date?: string) => string;
+  findBtcDataForSymbol?: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
+  
+  // Sorting props
+  onSort: (column: GenericSortColumn) => void;
+  getSortDirection: (column: GenericSortColumn) => SortDirection;
+  
+  // Filtering props
+  filterConfig: GenericFilterConfig;
+  onSymbolSearchChange: (search: string) => void;
+  onUsdSignalChange?: (signal: FilterConfig['usdSignal']) => void; // Only for crypto
+  onBtcSignalChange?: (signal: FilterConfig['btcSignal']) => void; // Only for crypto
+  onSignalChange?: (signal: StockFilterConfig['signal']) => void; // Only for stock
+  onClearFilters: () => void;
+  hasActiveFilters: boolean;
+}
+
+// Helper functions
+export const isStockData = (data: AssetData): data is StockStatisticsDto => {
+  return 'mapping' in data && (data.mapping === null || !('slug' in data.mapping));
+};
+
+export const isCryptoData = (data: AssetData): data is CryptoCurrencyStatisticsDto => {
+  return !isStockData(data);
+};
diff --git a/crypto-indicator-web/src/utils/chartHelpers.ts b/crypto-indicator-web/src/utils/chartHelpers.ts
index fb5a599..aa80025 100644
--- a/crypto-indicator-web/src/utils/chartHelpers.ts
+++ b/crypto-indicator-web/src/utils/chartHelpers.ts
@@ -11,7 +11,7 @@ import { SMMA_PERIODS } from "../constants/indicators";
 
 import { dataTransformers } from "./dataTransformers";
 
-import type { CryptoCurrencyStatisticsDto } from "../generated";
+import type { ChartDataSource } from "../types/chart";
 
 /**
  * Chart creation and configuration helpers
@@ -45,7 +45,7 @@ export const chartHelpers = {
    */
   setupChartSeries: (
     chart: IChartApi,
-    data: CryptoCurrencyStatisticsDto,
+    data: ChartDataSource,
   ): void => {
     const seriesConfig = getSeriesConfig();
 
@@ -55,6 +55,7 @@ export const chartHelpers = {
       seriesConfig.candlestick,
     );
     const candlestickData = dataTransformers.transformCandlestickData(
+      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access
       data.indicatorValues,
     );
 
@@ -71,6 +72,7 @@ export const chartHelpers = {
         });
 
         const smmaData = dataTransformers.transformSMMAData(
+          // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access
           data.indicatorValues,
           period,
         );
diff --git a/crypto-indicator-web/src/components/table/GenericDesktopLayout.tsx b/crypto-indicator-web/src/components/table/GenericDesktopLayout.tsx
new file mode 100644
index 0000000..0cee885
--- /dev/null
+++ b/crypto-indicator-web/src/components/table/GenericDesktopLayout.tsx
@@ -0,0 +1,76 @@
+import React from "react";
+
+import { CSS_CLASSES } from "../../constants/app";
+import type { GenericTableProps } from "../../types/genericTable";
+
+import { GenericTableFilters } from "./GenericTableFilters";
+import { GenericTableRow } from "./GenericTableRow";
+import { GenericSortableTableHeader } from "./GenericSortableTableHeader";
+
+interface GenericDesktopLayoutProps extends GenericTableProps {
+  showFilters: boolean;
+  onToggleFilters: () => void;
+  onCloseFilters: () => void;
+  filteredCount?: number;
+  totalCount?: number;
+}
+
+export const GenericDesktopLayout: React.FC<GenericDesktopLayoutProps> = ({
+  assetConfig,
+  data,
+  btcStatistics,
+  onSignalClick,
+  formatDate,
+  findBtcDataForSymbol,
+  onSort,
+  getSortDirection,
+  filterConfig,
+  onSymbolSearchChange,
+  onUsdSignalChange,
+  onBtcSignalChange,
+  onSignalChange,
+  onClearFilters,
+  hasActiveFilters,
+  filteredCount = data.length,
+  totalCount = data.length,
+}) => {
+  return (
+    <div className="desktop-layout">
+      <GenericTableFilters
+        assetConfig={assetConfig}
+        filterConfig={filterConfig}
+        onSymbolSearchChange={onSymbolSearchChange}
+        onUsdSignalChange={onUsdSignalChange}
+        onBtcSignalChange={onBtcSignalChange}
+        onSignalChange={onSignalChange}
+        onClearFilters={onClearFilters}
+        hasActiveFilters={hasActiveFilters}
+        filteredCount={filteredCount}
+        totalCount={totalCount}
+      />
+
+      <div className={CSS_CLASSES.TABLE_CONTAINER}>
+        <table className={CSS_CLASSES.TABLE}>
+          <GenericSortableTableHeader
+            assetConfig={assetConfig}
+            onSort={onSort}
+            getSortDirection={getSortDirection}
+          />
+          <tbody>
+            {data.map((item) => (
+              <GenericTableRow
+                key={item.symbol}
+                assetConfig={assetConfig}
+                data={item}
+                btcStatistics={btcStatistics}
+                onSignalClick={onSignalClick}
+                formatDate={formatDate}
+                findBtcDataForSymbol={findBtcDataForSymbol}
+              />
+            ))}
+          </tbody>
+        </table>
+      </div>
+    </div>
+  );
+};
diff --git a/crypto-indicator-web/src/components/table/GenericMobileLayout.tsx b/crypto-indicator-web/src/components/table/GenericMobileLayout.tsx
new file mode 100644
index 0000000..74f3d51
--- /dev/null
+++ b/crypto-indicator-web/src/components/table/GenericMobileLayout.tsx
@@ -0,0 +1,19 @@
+import React from "react";
+
+import type { GenericTableProps } from "../../types/genericTable";
+
+import { GenericDesktopLayout } from "./GenericDesktopLayout";
+
+interface GenericMobileLayoutProps extends GenericTableProps {
+  showFilters: boolean;
+  onToggleFilters: () => void;
+  onCloseFilters: () => void;
+  filteredCount?: number;
+  totalCount?: number;
+}
+
+// For now, use desktop layout as fallback
+// TODO: Implement proper mobile card layout
+export const GenericMobileLayout: React.FC<GenericMobileLayoutProps> = (props) => {
+  return <GenericDesktopLayout {...props} />;
+};
diff --git a/crypto-indicator-web/src/components/table/GenericResponsiveTableContainer.tsx b/crypto-indicator-web/src/components/table/GenericResponsiveTableContainer.tsx
new file mode 100644
index 0000000..922a078
--- /dev/null
+++ b/crypto-indicator-web/src/components/table/GenericResponsiveTableContainer.tsx
@@ -0,0 +1,67 @@
+import React, { useState } from 'react';
+
+import { useResponsiveLayout } from '../../hooks/useResponsiveLayout';
+import type { GenericTableProps } from '../../types/genericTable';
+
+import { GenericDesktopLayout } from './GenericDesktopLayout';
+import { GenericMobileLayout } from './GenericMobileLayout';
+import { GenericTabletLayout } from './GenericTabletLayout';
+
+// Component-specific styles
+import '../../styles/layout/responsive-layout.css';
+
+export const GenericResponsiveTableContainer: React.FC<GenericTableProps> = (props) => {
+  const [showFilters, setShowFilters] = useState(false);
+  const { isMobile, isTablet, isDesktop } = useResponsiveLayout();
+
+  const toggleFilters = () => {
+    setShowFilters(!showFilters);
+  };
+
+  const closeFilters = () => {
+    setShowFilters(false);
+  };
+
+  if (isMobile) {
+    return (
+      <GenericMobileLayout
+        {...props}
+        showFilters={showFilters}
+        onToggleFilters={toggleFilters}
+        onCloseFilters={closeFilters}
+      />
+    );
+  }
+
+  if (isTablet) {
+    return (
+      <GenericTabletLayout
+        {...props}
+        showFilters={showFilters}
+        onToggleFilters={toggleFilters}
+        onCloseFilters={closeFilters}
+      />
+    );
+  }
+
+  if (isDesktop) {
+    return (
+      <GenericDesktopLayout
+        {...props}
+        showFilters={showFilters}
+        onToggleFilters={toggleFilters}
+        onCloseFilters={closeFilters}
+      />
+    );
+  }
+
+  // Fallback to desktop layout
+  return (
+    <GenericDesktopLayout
+      {...props}
+      showFilters={showFilters}
+      onToggleFilters={toggleFilters}
+      onCloseFilters={closeFilters}
+    />
+  );
+};
diff --git a/crypto-indicator-web/src/components/table/GenericSortableTableHeader.tsx b/crypto-indicator-web/src/components/table/GenericSortableTableHeader.tsx
new file mode 100644
index 0000000..b8b97d0
--- /dev/null
+++ b/crypto-indicator-web/src/components/table/GenericSortableTableHeader.tsx
@@ -0,0 +1,89 @@
+import React from 'react';
+
+import type { AssetConfig, GenericSortColumn } from '../../types/genericTable';
+import type { SortDirection } from '../../types/table';
+
+interface GenericSortableTableHeaderProps {
+  assetConfig: AssetConfig;
+  onSort: (column: GenericSortColumn) => void;
+  getSortDirection: (column: GenericSortColumn) => SortDirection;
+}
+
+const getSortIcon = (direction: SortDirection): string => {
+  switch (direction) {
+    case 'asc': {
+      return '▲';
+    }
+    case 'desc': {
+      return '▼';
+    }
+    default: {
+      return '↕';
+    }
+  }
+};
+
+const getAriaSort = (direction: SortDirection | null): 'ascending' | 'descending' | 'none' => {
+  if (direction === 'asc') {return 'ascending';}
+  if (direction === 'desc') {return 'descending';}
+  return 'none';
+};
+
+export const GenericSortableTableHeader: React.FC<GenericSortableTableHeaderProps> = ({
+  assetConfig,
+  onSort,
+  getSortDirection,
+}) => {
+  const createSortableHeader = (
+    headerText: string,
+    column: GenericSortColumn,
+    className = 'sortable-header'
+  ) => {
+    const direction = getSortDirection(column);
+    const sortIcon = getSortIcon(direction);
+    const ariaSort = getAriaSort(direction);
+
+    return (
+      <th key={column} className={className}>
+        <button
+          type="button"
+          onClick={() => { onSort(column); }}
+          className="header-button"
+          aria-sort={ariaSort}
+        >
+          <span className="header-text">{headerText}</span>
+          <span className="sort-icon">{sortIcon}</span>
+        </button>
+      </th>
+    );
+  };
+
+  return (
+    <thead>
+      <tr>
+        {/* Asset column (always present) */}
+        {createSortableHeader(assetConfig.headers.asset, 'symbol')}
+        
+        {/* Price column (always present) */}
+        {createSortableHeader(assetConfig.headers.price, assetConfig.type === 'crypto' ? 'usdPrice' : 'price')}
+        
+        {/* Market Cap column (crypto only) */}
+        {assetConfig.showMarketCap && createSortableHeader(assetConfig.headers.marketCap!, 'marketCap')}
+        
+        {/* Volume column (stock only) */}
+        {assetConfig.showVolume && createSortableHeader(assetConfig.headers.volume!, 'volume')}
+        
+        {/* Signal column (always present) */}
+        {createSortableHeader(assetConfig.headers.signal, assetConfig.type === 'crypto' ? 'usdSignal' : 'signal')}
+        
+        {/* BTC columns (crypto only) */}
+        {assetConfig.showBtcColumns && (
+          <>
+            {createSortableHeader(assetConfig.headers.btcPrice!, 'btcPrice')}
+            {createSortableHeader(assetConfig.headers.btcSignal!, 'btcSignal')}
+          </>
+        )}
+      </tr>
+    </thead>
+  );
+};
diff --git a/crypto-indicator-web/src/components/table/GenericTableFilters.tsx b/crypto-indicator-web/src/components/table/GenericTableFilters.tsx
new file mode 100644
index 0000000..c6fdb95
--- /dev/null
+++ b/crypto-indicator-web/src/components/table/GenericTableFilters.tsx
@@ -0,0 +1,123 @@
+import React from 'react';
+
+import type { AssetConfig, GenericFilterConfig } from '../../types/genericTable';
+import type { FilterConfig, StockFilterConfig } from '../../types/table';
+
+// Component-specific styles
+import '../../styles/components/filters.css';
+
+interface GenericTableFiltersProps {
+  assetConfig: AssetConfig;
+  filterConfig: GenericFilterConfig;
+  onSymbolSearchChange: (search: string) => void;
+  onUsdSignalChange?: (signal: FilterConfig['usdSignal']) => void;
+  onBtcSignalChange?: (signal: FilterConfig['btcSignal']) => void;
+  onSignalChange?: (signal: StockFilterConfig['signal']) => void;
+  onClearFilters: () => void;
+  hasActiveFilters: boolean;
+  filteredCount: number;
+  totalCount: number;
+}
+
+// eslint-disable-next-line max-lines-per-function
+export const GenericTableFilters: React.FC<GenericTableFiltersProps> = ({
+  assetConfig,
+  filterConfig,
+  onSymbolSearchChange,
+  onUsdSignalChange,
+  onBtcSignalChange,
+  onSignalChange,
+  onClearFilters,
+  hasActiveFilters,
+  filteredCount,
+  totalCount,
+}) => {
+  const assetName = assetConfig.type === 'crypto' ? 'cryptocurrencies' : 'stocks';
+  const searchPlaceholder = `Search ${assetName}...`;
+
+  return (
+    <div className="table-filters">
+      <div className="filters-row">
+        <div className="filter-group">
+          <label htmlFor="symbol-search">Search:</label>
+          <input
+            id="symbol-search"
+            type="text"
+            placeholder={searchPlaceholder}
+            value={filterConfig.symbolSearch}
+            onChange={(e) => { onSymbolSearchChange(e.target.value); }}
+            className="filter-input"
+          />
+        </div>
+
+        {/* Crypto-specific filters */}
+        {assetConfig.type === 'crypto' && onUsdSignalChange && onBtcSignalChange && (
+          <>
+            <div className="filter-group">
+              <label htmlFor="usd-signal">USD Signal:</label>
+              <select
+                id="usd-signal"
+                value={(filterConfig as FilterConfig).usdSignal}
+                onChange={(e) => { onUsdSignalChange(e.target.value as FilterConfig['usdSignal']); }}
+                className="filter-select"
+              >
+                <option value="all">All</option>
+                <option value="gold">Gold (Bullish)</option>
+                <option value="blue">Blue (Bearish)</option>
+                <option value="gray">Gray (Neutral)</option>
+              </select>
+            </div>
+
+            <div className="filter-group">
+              <label htmlFor="btc-signal">BTC Signal:</label>
+              <select
+                id="btc-signal"
+                value={(filterConfig as FilterConfig).btcSignal}
+                onChange={(e) => { onBtcSignalChange(e.target.value as FilterConfig['btcSignal']); }}
+                className="filter-select"
+              >
+                <option value="all">All</option>
+                <option value="gold">Gold (Bullish)</option>
+                <option value="blue">Blue (Bearish)</option>
+                <option value="gray">Gray (Neutral)</option>
+              </select>
+            </div>
+          </>
+        )}
+
+        {/* Stock-specific filters */}
+        {assetConfig.type === 'stock' && onSignalChange && (
+          <div className="filter-group">
+            <label htmlFor="signal">Signal:</label>
+            <select
+              id="signal"
+              value={(filterConfig as StockFilterConfig).signal}
+              onChange={(e) => { onSignalChange(e.target.value as StockFilterConfig['signal']); }}
+              className="filter-select"
+            >
+              <option value="all">All Signals</option>
+              <option value="gold">Bullish (Gold)</option>
+              <option value="blue">Bearish (Blue)</option>
+              <option value="gray">Neutral (Gray)</option>
+            </select>
+          </div>
+        )}
+
+        {hasActiveFilters && (
+          <button
+            type="button"
+            onClick={onClearFilters}
+            className="clear-filters-btn"
+          >
+            Clear Filters
+          </button>
+        )}
+      </div>
+
+      <div className="filter-summary">
+        Showing {filteredCount} of {totalCount} {assetName}
+        {hasActiveFilters && ' (filtered)'}
+      </div>
+    </div>
+  );
+};
diff --git a/crypto-indicator-web/src/components/table/GenericTableRow.tsx b/crypto-indicator-web/src/components/table/GenericTableRow.tsx
new file mode 100644
index 0000000..9f1e90c
--- /dev/null
+++ b/crypto-indicator-web/src/components/table/GenericTableRow.tsx
@@ -0,0 +1,148 @@
+import React from "react";
+
+import { CSS_CLASSES, CURRENCIES } from "../../constants/app";
+import { formatters, navigation } from "../../utils/formatters";
+import { formatStockPrice, formatStockVolume } from "../../utils/stockDataProcessors";
+import { SignalBadge } from "../signals/SignalBadge";
+
+import type { AssetConfig, AssetData, isCryptoData, isStockData } from "../../types/genericTable";
+import type { CryptoCurrencyStatisticsDto, IndicatorValueDto } from "../../generated";
+
+interface GenericTableRowProps {
+  assetConfig: AssetConfig;
+  data: AssetData;
+  btcStatistics?: CryptoCurrencyStatisticsDto[];
+  onSignalClick: (symbol: string, currency: string) => void;
+  formatDate: (date?: string) => string;
+  findBtcDataForSymbol?: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
+}
+
+// eslint-disable-next-line max-lines-per-function
+export const GenericTableRow: React.FC<GenericTableRowProps> = ({
+  assetConfig,
+  data,
+  btcStatistics,
+  onSignalClick,
+  formatDate,
+  findBtcDataForSymbol,
+}) => {
+  const latestData = data.indicatorValues.find(Boolean);
+  
+  // Get BTC data for crypto assets
+  const btcData = assetConfig.type === 'crypto' && btcStatistics && findBtcDataForSymbol 
+    ? findBtcDataForSymbol(btcStatistics, data.symbol)
+    : undefined;
+
+  // Create tooltip text with last update information
+  const primaryTooltip = `Click to view chart${
+    (latestData?.timestamp !== null && latestData?.timestamp !== undefined) 
+      ? ` • Last update: ${formatDate(latestData.timestamp)}` 
+      : ""
+  }`;
+  
+  const btcTooltip = `Click to view chart${
+    (btcData?.timestamp !== null && btcData?.timestamp !== undefined) 
+      ? ` • Last update: ${formatDate(btcData.timestamp)}` 
+      : ""
+  }`;
+
+  const handleSymbolClick = () => {
+    if (assetConfig.type === 'crypto' && isCryptoData(data)) {
+      navigation.openCoinMarketCap(data.mapping?.slug);
+    }
+    // For stocks, we could add navigation to a stock info site
+  };
+
+  const getSymbolIcon = () => {
+    if (assetConfig.type === 'crypto') {
+      return '🔗';
+    }
+    return '📈';
+  };
+
+  const getSymbolTitle = () => {
+    if (assetConfig.type === 'crypto') {
+      return `🔗 Click to view ${data.symbol} on CoinMarketCap (opens in new tab)`;
+    }
+    return `📈 ${data.symbol} stock information`;
+  };
+
+  return (
+    <tr key={data.symbol}>
+      {/* Asset Symbol Column */}
+      <td data-label={assetConfig.headers.asset}>
+        <div className={CSS_CLASSES.SYMBOL_CELL}>
+          <button
+            className="clickable-symbol"
+            onClick={handleSymbolClick}
+            type="button"
+            title={getSymbolTitle()}
+            aria-label={`View ${data.symbol} information`}
+          >
+            {getSymbolIcon()}
+            <strong>{data.symbol}</strong>
+          </button>
+        </div>
+      </td>
+
+      {/* Price Column */}
+      <td data-label={assetConfig.headers.price}>
+        <strong>
+          {assetConfig.type === 'crypto' 
+            ? formatters.formatCurrency(latestData?.close, CURRENCIES.USD)
+            : formatStockPrice(latestData?.close, data.conversionCurrency)
+          }
+        </strong>
+      </td>
+
+      {/* Market Cap Column (Crypto only) */}
+      {assetConfig.showMarketCap && (
+        <td data-label={assetConfig.headers.marketCap}>
+          <strong>
+            {latestData?.marketCap 
+              ? formatters.formatMarketCap(latestData.marketCap)
+              : "-"
+            }
+          </strong>
+        </td>
+      )}
+
+      {/* Volume Column (Stock only) */}
+      {assetConfig.showVolume && (
+        <td data-label={assetConfig.headers.volume}>
+          <strong>{formatStockVolume(latestData?.volume)}</strong>
+        </td>
+      )}
+
+      {/* Primary Signal Column */}
+      <td data-label={assetConfig.headers.signal}>
+        <SignalBadge
+          signal={latestData?.color}
+          onClick={() => { onSignalClick(data.symbol, data.conversionCurrency); }}
+          tooltip={primaryTooltip}
+        />
+      </td>
+
+      {/* BTC Columns (Crypto only) */}
+      {assetConfig.showBtcColumns && (
+        <>
+          <td data-label={assetConfig.headers.btcPrice}>
+            <strong>
+              {btcData?.close 
+                ? formatters.formatCurrency(btcData.close, CURRENCIES.BTC)
+                : "-"
+              }
+            </strong>
+          </td>
+          <td data-label={assetConfig.headers.btcSignal}>
+            <SignalBadge
+              signal={btcData?.color}
+              onClick={() => { onSignalClick(data.symbol, CURRENCIES.BTC); }}
+              tooltip={btcTooltip}
+            />
+          </td>
+        </>
+      )}
+    </tr>
+  );
+};
diff --git a/crypto-indicator-web/src/components/table/GenericTabletLayout.tsx b/crypto-indicator-web/src/components/table/GenericTabletLayout.tsx
new file mode 100644
index 0000000..dc844e1
--- /dev/null
+++ b/crypto-indicator-web/src/components/table/GenericTabletLayout.tsx
@@ -0,0 +1,19 @@
+import React from "react";
+
+import type { GenericTableProps } from "../../types/genericTable";
+
+import { GenericDesktopLayout } from "./GenericDesktopLayout";
+
+interface GenericTabletLayoutProps extends GenericTableProps {
+  showFilters: boolean;
+  onToggleFilters: () => void;
+  onCloseFilters: () => void;
+  filteredCount?: number;
+  totalCount?: number;
+}
+
+// For now, use desktop layout as fallback
+// TODO: Implement proper tablet layout
+export const GenericTabletLayout: React.FC<GenericTabletLayoutProps> = (props) => {
+  return <GenericDesktopLayout {...props} />;
+};
diff --git a/crypto-indicator-web/src/types/genericTable.ts b/crypto-indicator-web/src/types/genericTable.ts
new file mode 100644
index 0000000..b08ede1
--- /dev/null
+++ b/crypto-indicator-web/src/types/genericTable.ts
@@ -0,0 +1,89 @@
+import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from '../generated';
+import type { FilterConfig, SortColumn, SortDirection, StockFilterConfig, StockSortColumn } from './table';
+
+// Generic data types
+export type AssetData = CryptoCurrencyStatisticsDto | StockStatisticsDto;
+export type AssetType = 'crypto' | 'stock';
+
+// Generic sort and filter types
+export type GenericSortColumn = SortColumn | StockSortColumn;
+export type GenericFilterConfig = FilterConfig | StockFilterConfig;
+
+// Asset-specific configurations
+export interface AssetConfig {
+  type: AssetType;
+  headers: {
+    asset: string; // "Cryptocurrency" or "Stock"
+    price: string; // "USD Price" or "Price"
+    volume?: string; // "Volume" (only for stocks)
+    marketCap?: string; // "Market Cap" (only for crypto)
+    signal: string; // "USD Signal" or "Signal"
+    btcPrice?: string; // "BTC Price" (only for crypto)
+    btcSignal?: string; // "BTC Signal" (only for crypto)
+  };
+  showBtcColumns: boolean;
+  showMarketCap: boolean;
+  showVolume: boolean;
+}
+
+// Crypto configuration
+export const CRYPTO_CONFIG: AssetConfig = {
+  type: 'crypto',
+  headers: {
+    asset: 'Cryptocurrency',
+    price: 'USD Price',
+    marketCap: 'Market Cap',
+    signal: 'USD Signal',
+    btcPrice: 'BTC Price',
+    btcSignal: 'BTC Signal',
+  },
+  showBtcColumns: true,
+  showMarketCap: true,
+  showVolume: false,
+};
+
+// Stock configuration
+export const STOCK_CONFIG: AssetConfig = {
+  type: 'stock',
+  headers: {
+    asset: 'Stock',
+    price: 'Price',
+    volume: 'Volume',
+    signal: 'Signal',
+  },
+  showBtcColumns: false,
+  showMarketCap: false,
+  showVolume: true,
+};
+
+// Generic table props interface
+export interface GenericTableProps {
+  assetConfig: AssetConfig;
+  data: AssetData[];
+  btcStatistics?: CryptoCurrencyStatisticsDto[]; // Only needed for crypto
+  onSignalClick: (symbol: string, currency: string) => void;
+  formatDate: (date?: string) => string;
+  findBtcDataForSymbol?: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
+  
+  // Sorting props
+  onSort: (column: GenericSortColumn) => void;
+  getSortDirection: (column: GenericSortColumn) => SortDirection;
+  
+  // Filtering props
+  filterConfig: GenericFilterConfig;
+  onSymbolSearchChange: (search: string) => void;
+  onUsdSignalChange?: (signal: FilterConfig['usdSignal']) => void; // Only for crypto
+  onBtcSignalChange?: (signal: FilterConfig['btcSignal']) => void; // Only for crypto
+  onSignalChange?: (signal: StockFilterConfig['signal']) => void; // Only for stock
+  onClearFilters: () => void;
+  hasActiveFilters: boolean;
+}
+
+// Helper functions
+export const isStockData = (data: AssetData): data is StockStatisticsDto => {
+  return 'mapping' in data && (data.mapping === null || !('slug' in data.mapping));
+};
+
+export const isCryptoData = (data: AssetData): data is CryptoCurrencyStatisticsDto => {
+  return !isStockData(data);
+};
