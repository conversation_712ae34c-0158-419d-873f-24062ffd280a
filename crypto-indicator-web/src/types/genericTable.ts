import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from '../generated';
import type { FilterConfig, SortColumn, SortDirection, StockFilterConfig, StockSortColumn } from './table';

// Generic data types
export type AssetData = CryptoCurrencyStatisticsDto | StockStatisticsDto;
export type AssetType = 'crypto' | 'stock';

// Generic sort and filter types
export type GenericSortColumn = SortColumn | StockSortColumn;
export type GenericFilterConfig = FilterConfig | StockFilterConfig;

// Asset-specific configurations
export interface AssetConfig {
  type: AssetType;
  headers: {
    asset: string; // "Cryptocurrency" or "Stock"
    price: string; // "USD Price" or "Price"
    volume?: string; // "Volume" (only for stocks)
    marketCap?: string; // "Market Cap" (only for crypto)
    signal: string; // "USD Signal" or "Signal"
    btcPrice?: string; // "BTC Price" (only for crypto)
    btcSignal?: string; // "BTC Signal" (only for crypto)
  };
  showBtcColumns: boolean;
  showMarketCap: boolean;
  showVolume: boolean;
}

// Crypto configuration
export const CRYPTO_CONFIG: AssetConfig = {
  type: 'crypto',
  headers: {
    asset: 'Cryptocurrency',
    price: 'USD Price',
    marketCap: 'Market Cap',
    signal: 'USD Signal',
    btcPrice: 'BTC Price',
    btcSignal: 'BTC Signal',
  },
  showBtcColumns: true,
  showMarketCap: true,
  showVolume: false,
};

// Stock configuration
export const STOCK_CONFIG: AssetConfig = {
  type: 'stock',
  headers: {
    asset: 'Stock',
    price: 'Price',
    volume: 'Volume',
    signal: 'Signal',
  },
  showBtcColumns: false,
  showMarketCap: false,
  showVolume: true,
};

// Generic table props interface
export interface GenericTableProps {
  assetConfig: AssetConfig;
  data: AssetData[];
  btcStatistics?: CryptoCurrencyStatisticsDto[]; // Only needed for crypto
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol?: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
  
  // Sorting props
  onSort: (column: GenericSortColumn) => void;
  getSortDirection: (column: GenericSortColumn) => SortDirection;
  
  // Filtering props
  filterConfig: GenericFilterConfig;
  onSymbolSearchChange: (search: string) => void;
  onUsdSignalChange?: (signal: FilterConfig['usdSignal']) => void; // Only for crypto
  onBtcSignalChange?: (signal: FilterConfig['btcSignal']) => void; // Only for crypto
  onSignalChange?: (signal: StockFilterConfig['signal']) => void; // Only for stock
  onClearFilters: () => void;
  hasActiveFilters: boolean;
}

// Helper functions
export const isStockData = (data: AssetData): data is StockStatisticsDto => {
  return 'mapping' in data && (data.mapping === null || !('slug' in data.mapping));
};

export const isCryptoData = (data: AssetData): data is CryptoCurrencyStatisticsDto => {
  return !isStockData(data);
};
