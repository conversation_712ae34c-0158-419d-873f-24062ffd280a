import React from 'react';

import { ResponsiveTableContainer } from '../table/ResponsiveTableContainer';

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto,StockStatisticsDto } from '../../generated';
import type { StockFilterConfig, StockSortColumn } from '../../types/table';

// Dummy formatDate function for stocks (can be enhanced later)
const formatDate = (date?: string): string => {
  if (date === null || date === undefined || date === '') {
    return '';
  }
  return new Date(date).toLocaleDateString();
};

interface StockMainContentProps {
  data: StockStatisticsDto[];
  totalCount: number;
  filteredCount: number;
  loading: boolean;
  filterConfig: StockFilterConfig;
  hasActiveFilters: boolean;
  onSignalClick: (symbol: string, conversionCurrency: string) => void;
  onRefresh: () => void;
  onSort: (column: StockSortColumn) => void;
  getSortDirection: (column: StockSortColumn) => 'asc' | 'desc' | null;
  onSymbolSearchChange: (search: string) => void;
  onSignalChange: (signal: 'all' | 'gold' | 'blue' | 'gray') => void;
  onBtcSignalChange: (signal: 'all' | 'gold' | 'blue' | 'gray') => void;
  onClearFilters: () => void;
  btcStatistics: CryptoCurrencyStatisticsDto[];
  findBtcDataForSymbol: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
}

export const StockMainContent: React.FC<StockMainContentProps> = ({
  data,
  totalCount,
  filteredCount,
  loading,
  filterConfig,
  hasActiveFilters,
  onSignalClick,
  onRefresh: _onRefresh,
  onSort,
  getSortDirection,
  onSymbolSearchChange,
  onSignalChange,
  onBtcSignalChange,
  onClearFilters,
  btcStatistics,
  findBtcDataForSymbol,
}) => {
  return (
    <div className="stock-main-content">
      <ResponsiveTableContainer
        data={data}
        btcStatistics={btcStatistics} // Pass BTC data for stocks too - user wants identical table structure
        onSignalClick={onSignalClick}
        formatDate={formatDate}
        findBtcDataForSymbol={findBtcDataForSymbol} // Use the same BTC data function
        onSort={onSort}
        getSortDirection={getSortDirection}
        filterConfig={filterConfig}
        onSymbolSearchChange={onSymbolSearchChange}
        onUsdSignalChange={onSignalChange} // Map stock signal to USD signal
        onBtcSignalChange={onBtcSignalChange} // Handle BTC signal changes for stocks
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
        assetType="stock" // Add this prop to distinguish between crypto and stock
      />

      {data.length === 0 && !loading && (
        <div className="no-data">
          <p>No stock data available.</p>
        </div>
      )}
    </div>
  );
};
