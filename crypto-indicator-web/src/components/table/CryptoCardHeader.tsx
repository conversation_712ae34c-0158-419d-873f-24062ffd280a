import React from "react";

import { CSS_CLASSES,CURRENCIES } from "../../constants/app";
import { formatters } from "../../utils/formatters";

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from "../../generated";

interface CryptoCardHeaderProps {
  crypto: CryptoCurrencyStatisticsDto | StockStatisticsDto;
  usdData: IndicatorValueDto | undefined;
  onSymbolClick: (e: React.MouseEvent) => void;
  assetType?: 'crypto' | 'stock';
}

export const CryptoCardHeader: React.FC<CryptoCardHeaderProps> = ({
  crypto,
  usdData,
  onSymbolClick,
  assetType = 'crypto',
}) => {
  return (
    <div className="crypto-card-header">
      <div className="crypto-card-symbol">
        <div className={CSS_CLASSES.SYMBOL_CELL}>
          <button
            className="clickable-symbol"
            onClick={onSymbolClick}
            type="button"
            title={`🔗 Click to view ${crypto.symbol} on CoinMarketCap (opens in new tab)`}
            aria-label={`View ${crypto.symbol} on CoinMarketCap`}
          >
            <strong>{crypto.symbol}</strong>
          </button>
        </div>
      </div>
      <div className="crypto-card-price">
        <span className="price-label">{assetType === 'crypto' ? 'USD' : 'Price'}</span>
        <span className="price-value">
          {formatters.price(usdData?.close, assetType === 'crypto' ? CURRENCIES.USD : crypto.conversionCurrency)}
        </span>
      </div>
    </div>
  );
};
