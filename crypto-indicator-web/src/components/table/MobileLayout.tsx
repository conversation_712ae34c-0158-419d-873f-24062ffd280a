import React from "react";

import { CryptoCard } from "./CryptoCard";
import { FilterDrawer } from "./FilterDrawer";
import { FilterToggle } from "./FilterToggle";

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from "../../generated";
import type { FilterConfig, StockFilterConfig } from "../../types/table";

interface MobileLayoutProps {
  data: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  btcStatistics: CryptoCurrencyStatisticsDto[];
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
  filterConfig: FilterConfig | StockFilterConfig;
  onUsdSignalChange: (signal: FilterConfig["usdSignal"] | StockFilterConfig["signal"]) => void;
  onBtcSignalChange: (signal: FilterConfig["btcSignal"]) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
  isFilterDrawerOpen: boolean;
  onFilterDrawerToggle: (open: boolean) => void;
  assetType?: 'crypto' | 'stock';
}

export const MobileLayout: React.FC<MobileLayoutProps> = ({
  data,
  btcStatistics,
  onSignalClick,
  formatDate,
  findBtcDataForSymbol,
  filterConfig,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
  isFilterDrawerOpen,
  onFilterDrawerToggle,
  assetType = 'crypto',
}) => {
  return (
    <div className="mobile-layout">
      <FilterToggle
        onClick={() => { onFilterDrawerToggle(true); }}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
      />
      
      <div className="crypto-cards-container">
        {data.map((item) => {
          const btcData = assetType === 'crypto' ? findBtcDataForSymbol(btcStatistics, item.symbol) : undefined;
          return (
            <CryptoCard
              key={item.symbol}
              crypto={item as CryptoCurrencyStatisticsDto}
              {...(btcData && { btcData })}
              onSignalClick={onSignalClick}
              formatDate={formatDate}
              assetType={assetType}
            />
          );
        })}
      </div>

      <FilterDrawer
        isOpen={isFilterDrawerOpen}
        onClose={() => { onFilterDrawerToggle(false); }}
        filterConfig={filterConfig}
        onUsdSignalChange={onUsdSignalChange}
        onBtcSignalChange={onBtcSignalChange}
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
        assetType={assetType}
      />
    </div>
  );
};
