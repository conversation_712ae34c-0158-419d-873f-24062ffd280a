import React from "react";

import { CSS_CLASSES,CURRENCIES } from "../../constants/app";
import { formatters, navigation } from "../../utils/formatters";
import { SignalBadge } from "../signals/SignalBadge";

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from "../../generated";

interface CryptoTableRowProps {
  crypto: CryptoCurrencyStatisticsDto | StockStatisticsDto;
  btcData?: IndicatorValueDto;
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
  assetType?: 'crypto' | 'stock';
}

// eslint-disable-next-line max-lines-per-function
export const CryptoTableRow: React.FC<CryptoTableRowProps> = ({
  crypto,
  btcData,
  onSignalClick,
  formatDate,
  assetType = 'crypto',
}) => {
  const usdData = crypto.indicatorValues.find(Boolean);

  // Create tooltip text with last update information
  const usdTooltip = `Click to view chart${
    (usdData?.timestamp !== null && usdData?.timestamp !== undefined) ? ` • Last update: ${formatDate(usdData.timestamp)}` : ""
  }`;
  const btcTooltip = `Click to view chart${
    (btcData?.timestamp !== null && btcData?.timestamp !== undefined) ? ` • Last update: ${formatDate(btcData.timestamp)}` : ""
  }`;

  const handleSymbolClick = () => {
    if (assetType === 'crypto' && 'mapping' in crypto && typeof crypto.mapping?.slug === 'string') {
      navigation.openCoinMarketCap(crypto.mapping.slug);
    }
    // For stocks, we could add navigation to a stock info site
  };

  const getSymbolIcon = () => {
    return assetType === 'crypto' ? '🔗' : '📈';
  };

  const getSymbolTitle = () => {
    if (assetType === 'crypto') {
      return `🔗 Click to view ${crypto.symbol} on CoinMarketCap (opens in new tab)`;
    }
    return `📈 ${crypto.symbol} stock information`;
  };

  const getAssetLabel = () => {
    return assetType === 'crypto' ? 'Cryptocurrency' : 'Stock';
  };

  const getPriceLabel = () => {
    return assetType === 'crypto' ? 'USD Price' : 'Price';
  };

  const getSignalLabel = () => {
    return assetType === 'crypto' ? 'USD Signal' : 'Signal';
  };

  return (
    <tr key={crypto.symbol}>
      <td data-label={getAssetLabel()}>
        <div className={CSS_CLASSES.SYMBOL_CELL}>
          <button
            className="clickable-symbol"
            onClick={handleSymbolClick}
            type="button"
            title={getSymbolTitle()}
            aria-label={`View ${crypto.symbol} information`}
          >
            {getSymbolIcon()}
            <strong>{crypto.symbol}</strong>
          </button>
        </div>
      </td>
      <td data-label={getPriceLabel()}>
        <strong>{formatters.price(usdData?.close, assetType === 'crypto' ? CURRENCIES.USD : crypto.conversionCurrency)}</strong>
      </td>
      {assetType === 'crypto' && (
        <td data-label="Market Cap">
          <strong>{formatters.marketCap(typeof usdData?.marketCap === 'number' ? usdData.marketCap : 0)}</strong>
        </td>
      )}
      {assetType === 'stock' && (
        <td data-label="Volume">
          <strong>{formatters.volume(usdData?.volume)}</strong>
        </td>
      )}
      <td data-label={getSignalLabel()}>
        <SignalBadge
          color={usdData?.color}
          onClick={() => { onSignalClick(crypto.symbol, assetType === 'crypto' ? CURRENCIES.USD : crypto.conversionCurrency); }}
          clickable
          title={usdTooltip}
        />
      </td>
      {/* BTC columns for both crypto and stock - user wants identical table structure */}
      <td data-label="BTC Price">
        <strong>{formatters.price(btcData?.close, CURRENCIES.BTC)}</strong>
      </td>
      <td data-label="BTC Signal">
        <SignalBadge
          color={btcData?.color}
          onClick={() => { onSignalClick(crypto.symbol, CURRENCIES.BTC); }}
          clickable
          title={btcTooltip}
        />
      </td>
    </tr>
  );
};
