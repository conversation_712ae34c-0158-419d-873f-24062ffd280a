import React from 'react';

import type { AssetConfig, GenericFilterConfig } from '../../types/genericTable';
import type { FilterConfig, StockFilterConfig } from '../../types/table';

// Component-specific styles
import '../../styles/components/filters.css';

interface GenericTableFiltersProps {
  assetConfig: AssetConfig;
  filterConfig: GenericFilterConfig;
  onSymbolSearchChange: (search: string) => void;
  onUsdSignalChange?: (signal: FilterConfig['usdSignal']) => void;
  onBtcSignalChange?: (signal: FilterConfig['btcSignal']) => void;
  onSignalChange?: (signal: StockFilterConfig['signal']) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
}

// eslint-disable-next-line max-lines-per-function
export const GenericTableFilters: React.FC<GenericTableFiltersProps> = ({
  assetConfig,
  filterConfig,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
}) => {
  const assetName = assetConfig.type === 'crypto' ? 'cryptocurrencies' : 'stocks';
  const searchPlaceholder = `Search ${assetName}...`;

  return (
    <div className="table-filters">
      <div className="filters-row">
        <div className="filter-group">
          <label htmlFor="symbol-search">Search:</label>
          <input
            id="symbol-search"
            type="text"
            placeholder={searchPlaceholder}
            value={filterConfig.symbolSearch}
            onChange={(e) => { onSymbolSearchChange(e.target.value); }}
            className="filter-input"
          />
        </div>

        {/* Crypto-specific filters */}
        {assetConfig.type === 'crypto' && onUsdSignalChange && onBtcSignalChange && (
          <>
            <div className="filter-group">
              <label htmlFor="usd-signal">USD Signal:</label>
              <select
                id="usd-signal"
                value={(filterConfig as FilterConfig).usdSignal}
                onChange={(e) => { onUsdSignalChange(e.target.value as FilterConfig['usdSignal']); }}
                className="filter-select"
              >
                <option value="all">All</option>
                <option value="gold">Gold (Bullish)</option>
                <option value="blue">Blue (Bearish)</option>
                <option value="gray">Gray (Neutral)</option>
              </select>
            </div>

            <div className="filter-group">
              <label htmlFor="btc-signal">BTC Signal:</label>
              <select
                id="btc-signal"
                value={(filterConfig as FilterConfig).btcSignal}
                onChange={(e) => { onBtcSignalChange(e.target.value as FilterConfig['btcSignal']); }}
                className="filter-select"
              >
                <option value="all">All</option>
                <option value="gold">Gold (Bullish)</option>
                <option value="blue">Blue (Bearish)</option>
                <option value="gray">Gray (Neutral)</option>
              </select>
            </div>
          </>
        )}

        {/* Stock-specific filters */}
        {assetConfig.type === 'stock' && onSignalChange && (
          <div className="filter-group">
            <label htmlFor="signal">Signal:</label>
            <select
              id="signal"
              value={(filterConfig as StockFilterConfig).signal}
              onChange={(e) => { onSignalChange(e.target.value as StockFilterConfig['signal']); }}
              className="filter-select"
            >
              <option value="all">All Signals</option>
              <option value="gold">Bullish (Gold)</option>
              <option value="blue">Bearish (Blue)</option>
              <option value="gray">Neutral (Gray)</option>
            </select>
          </div>
        )}

        {hasActiveFilters && (
          <button
            type="button"
            onClick={onClearFilters}
            className="clear-filters-btn"
          >
            Clear Filters
          </button>
        )}
      </div>

      <div className="filter-summary">
        Showing {filteredCount} of {totalCount} {assetName}
        {hasActiveFilters && ' (filtered)'}
      </div>
    </div>
  );
};
