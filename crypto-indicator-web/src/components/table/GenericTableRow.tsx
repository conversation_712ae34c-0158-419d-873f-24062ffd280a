import React from "react";

import { CSS_CLASSES, CURRENCIES } from "../../constants/app";
import { isCryptoData } from "../../types/genericTable";
import { formatters, navigation } from "../../utils/formatters";
import { formatStockPrice, formatStockVolume } from "../../utils/stockDataProcessors";
import { SignalBadge } from "../signals/SignalBadge";

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto } from "../../generated";
import type { AssetConfig, AssetData } from "../../types/genericTable";

interface GenericTableRowProps {
  assetConfig: AssetConfig;
  data: AssetData;
  btcStatistics?: CryptoCurrencyStatisticsDto[];
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol?: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
}

// eslint-disable-next-line max-lines-per-function
export const GenericTableRow: React.FC<GenericTableRowProps> = ({
  assetConfig,
  data,
  btcStatistics,
  onSignalClick,
  formatDate,
  findBtcDataForSymbol,
}) => {
  const latestData = data.indicatorValues.find(Boolean);
  
  // Get BTC data for crypto assets
  const btcData = assetConfig.type === 'crypto' && btcStatistics && findBtcDataForSymbol 
    ? findBtcDataForSymbol(btcStatistics, data.symbol)
    : undefined;

  // Create tooltip text with last update information
  const primaryTooltip = `Click to view chart${
    (latestData?.timestamp !== null && latestData?.timestamp !== undefined) 
      ? ` • Last update: ${formatDate(latestData.timestamp)}` 
      : ""
  }`;
  
  const btcTooltip = `Click to view chart${
    (btcData?.timestamp !== null && btcData?.timestamp !== undefined) 
      ? ` • Last update: ${formatDate(btcData.timestamp)}` 
      : ""
  }`;

  const handleSymbolClick = () => {
    if (assetConfig.type === 'crypto' && isCryptoData(data)) {
      navigation.openCoinMarketCap(data.mapping?.slug);
    }
    // For stocks, we could add navigation to a stock info site
  };

  const getSymbolIcon = () => {
    if (assetConfig.type === 'crypto') {
      return '🔗';
    }
    return '📈';
  };

  const getSymbolTitle = () => {
    if (assetConfig.type === 'crypto') {
      return `🔗 Click to view ${data.symbol} on CoinMarketCap (opens in new tab)`;
    }
    return `📈 ${data.symbol} stock information`;
  };

  return (
    <tr key={data.symbol}>
      {/* Asset Symbol Column */}
      <td data-label={assetConfig.headers.asset}>
        <div className={CSS_CLASSES.SYMBOL_CELL}>
          <button
            className="clickable-symbol"
            onClick={handleSymbolClick}
            type="button"
            title={getSymbolTitle()}
            aria-label={`View ${data.symbol} information`}
          >
            {getSymbolIcon()}
            <strong>{data.symbol}</strong>
          </button>
        </div>
      </td>

      {/* Price Column */}
      <td data-label={assetConfig.headers.price}>
        <strong>
          {assetConfig.type === 'crypto' 
            ? formatters.formatCurrency(latestData?.close, CURRENCIES.USD)
            : formatStockPrice(latestData?.close, data.conversionCurrency)
          }
        </strong>
      </td>

      {/* Market Cap Column (Crypto only) */}
      {assetConfig.showMarketCap && (
        <td data-label={assetConfig.headers.marketCap}>
          <strong>
            {latestData?.marketCap 
              ? formatters.formatMarketCap(latestData.marketCap)
              : "-"
            }
          </strong>
        </td>
      )}

      {/* Volume Column (Stock only) */}
      {assetConfig.showVolume && (
        <td data-label={assetConfig.headers.volume}>
          <strong>{formatStockVolume(latestData?.volume)}</strong>
        </td>
      )}

      {/* Primary Signal Column */}
      <td data-label={assetConfig.headers.signal}>
        <SignalBadge
          signal={latestData?.color}
          onClick={() => { onSignalClick(data.symbol, data.conversionCurrency); }}
          tooltip={primaryTooltip}
        />
      </td>

      {/* BTC Columns (Crypto only) */}
      {assetConfig.showBtcColumns && (
        <>
          <td data-label={assetConfig.headers.btcPrice}>
            <strong>
              {btcData?.close 
                ? formatters.formatCurrency(btcData.close, CURRENCIES.BTC)
                : "-"
              }
            </strong>
          </td>
          <td data-label={assetConfig.headers.btcSignal}>
            <SignalBadge
              signal={btcData?.color}
              onClick={() => { onSignalClick(data.symbol, CURRENCIES.BTC); }}
              tooltip={btcTooltip}
            />
          </td>
        </>
      )}
    </tr>
  );
};
