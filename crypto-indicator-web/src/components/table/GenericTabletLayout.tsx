import React from "react";

import { GenericDesktopLayout } from "./GenericDesktopLayout";

import type { GenericTableProps } from "../../types/genericTable";

interface GenericTabletLayoutProps extends GenericTableProps {
  showFilters: boolean;
  onToggleFilters: () => void;
  onCloseFilters: () => void;
  filteredCount?: number;
  totalCount?: number;
}

// For now, use desktop layout as fallback
// TODO: Implement proper tablet layout
export const GenericTabletLayout: React.FC<GenericTabletLayoutProps> = (props) => {
  return <GenericDesktopLayout {...props} />;
};
