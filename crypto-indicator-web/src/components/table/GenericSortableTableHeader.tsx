import React from 'react';

import type { AssetConfig, GenericSortColumn } from '../../types/genericTable';
import type { SortDirection } from '../../types/table';

interface GenericSortableTableHeaderProps {
  assetConfig: AssetConfig;
  onSort: (column: GenericSortColumn) => void;
  getSortDirection: (column: GenericSortColumn) => SortDirection;
}

const getSortIcon = (direction: SortDirection): string => {
  switch (direction) {
    case 'asc': {
      return '▲';
    }
    case 'desc': {
      return '▼';
    }
    default: {
      return '↕';
    }
  }
};

const getAriaSort = (direction: SortDirection | null): 'ascending' | 'descending' | 'none' => {
  if (direction === 'asc') {return 'ascending';}
  if (direction === 'desc') {return 'descending';}
  return 'none';
};

export const GenericSortableTableHeader: React.FC<GenericSortableTableHeaderProps> = ({
  assetConfig,
  onSort,
  getSortDirection,
}) => {
  const createSortableHeader = (
    headerText: string,
    column: GenericSortColumn,
    className = 'sortable-header'
  ) => {
    const direction = getSortDirection(column);
    const sortIcon = getSortIcon(direction);
    const ariaSort = getAriaSort(direction);

    return (
      <th key={column} className={className}>
        <button
          type="button"
          onClick={() => { onSort(column); }}
          className="header-button"
          aria-sort={ariaSort}
        >
          <span className="header-text">{headerText}</span>
          <span className="sort-icon">{sortIcon}</span>
        </button>
      </th>
    );
  };

  return (
    <thead>
      <tr>
        {/* Asset column (always present) */}
        {createSortableHeader(assetConfig.headers.asset, 'symbol')}
        
        {/* Price column (always present) */}
        {createSortableHeader(assetConfig.headers.price, assetConfig.type === 'crypto' ? 'usdPrice' : 'price')}
        
        {/* Market Cap column (crypto only) */}
        {assetConfig.showMarketCap && createSortableHeader(assetConfig.headers.marketCap!, 'marketCap')}
        
        {/* Volume column (stock only) */}
        {assetConfig.showVolume && createSortableHeader(assetConfig.headers.volume!, 'volume')}
        
        {/* Signal column (always present) */}
        {createSortableHeader(assetConfig.headers.signal, assetConfig.type === 'crypto' ? 'usdSignal' : 'signal')}
        
        {/* BTC columns (crypto only) */}
        {assetConfig.showBtcColumns && (
          <>
            {createSortableHeader(assetConfig.headers.btcPrice!, 'btcPrice')}
            {createSortableHeader(assetConfig.headers.btcSignal!, 'btcSignal')}
          </>
        )}
      </tr>
    </thead>
  );
};
