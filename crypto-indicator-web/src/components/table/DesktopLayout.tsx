import React from "react";

import { CSS_CLASSES } from "../../constants/app";

import { CryptoTableRow } from "./CryptoTableRow";
import { SortableTableHeader } from "./SortableTableHeader";
import { TableFilters } from "./TableFilters";

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from "../../generated";
import type { FilterConfig, SortColumn, SortDirection, StockFilterConfig, StockSortColumn } from "../../types/table";

interface DesktopLayoutProps {
  data: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  btcStatistics: CryptoCurrencyStatisticsDto[];
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
  onSort: (column: SortColumn | StockSortColumn) => void;
  getSortDirection: (column: SortColumn | StockSortColumn) => SortDirection;
  filterConfig: FilterConfig | StockFilterConfig;
  onSymbolSearchChange: (search: string) => void;
  onUsdSignalChange: (signal: FilterConfig["usdSignal"] | StockFilterConfig["signal"]) => void;
  onBtcSignalChange: (signal: FilterConfig["btcSignal"]) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
  assetType?: 'crypto' | 'stock';
}

export const DesktopLayout: React.FC<DesktopLayoutProps> = ({
  data,
  btcStatistics,
  onSignalClick,
  formatDate,
  findBtcDataForSymbol,
  onSort,
  getSortDirection,
  filterConfig,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
  assetType = 'crypto',
}) => {
  return (
    <div className="desktop-layout">
      <TableFilters
        filterConfig={filterConfig}
        onSymbolSearchChange={onSymbolSearchChange}
        onUsdSignalChange={onUsdSignalChange}
        onBtcSignalChange={onBtcSignalChange}
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
        assetType={assetType}
      />

      <div className="table-wrapper">
        <table className={CSS_CLASSES.TABLE}>
          <SortableTableHeader
            onSort={onSort}
            getSortDirection={getSortDirection}
            assetType={assetType}
          />
          <tbody>
            {data.map((item) => {
              const btcData = assetType === 'crypto' ? findBtcDataForSymbol(btcStatistics, item.symbol) : undefined;
              return (
                <CryptoTableRow
                  key={item.symbol}
                  crypto={item as CryptoCurrencyStatisticsDto}
                  {...(btcData && { btcData })}
                  onSignalClick={onSignalClick}
                  formatDate={formatDate}
                  assetType={assetType}
                />
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};
