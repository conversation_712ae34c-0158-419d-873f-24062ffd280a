import React, { useState } from 'react';

import { useResponsiveLayout } from '../../hooks/useResponsiveLayout';

import { GenericDesktopLayout } from './GenericDesktopLayout';
import { GenericMobileLayout } from './GenericMobileLayout';
import { GenericTabletLayout } from './GenericTabletLayout';

import type { GenericTableProps } from '../../types/genericTable';

// Component-specific styles
import '../../styles/layout/responsive-layout.css';

export const GenericResponsiveTableContainer: React.FC<GenericTableProps> = (props) => {
  const [showFilters, setShowFilters] = useState(false);
  const { isMobile, isTablet, isDesktop } = useResponsiveLayout();

  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  const closeFilters = () => {
    setShowFilters(false);
  };

  if (isMobile) {
    return (
      <GenericMobileLayout
        {...props}
        showFilters={showFilters}
        onToggleFilters={toggleFilters}
        onCloseFilters={closeFilters}
      />
    );
  }

  if (isTablet) {
    return (
      <GenericTabletLayout
        {...props}
        showFilters={showFilters}
        onToggleFilters={toggleFilters}
        onCloseFilters={closeFilters}
      />
    );
  }

  if (isDesktop) {
    return (
      <GenericDesktopLayout
        {...props}
        showFilters={showFilters}
        onToggleFilters={toggleFilters}
        onCloseFilters={closeFilters}
      />
    );
  }

  // Fallback to desktop layout
  return (
    <GenericDesktopLayout
      {...props}
      showFilters={showFilters}
      onToggleFilters={toggleFilters}
      onCloseFilters={closeFilters}
    />
  );
};
