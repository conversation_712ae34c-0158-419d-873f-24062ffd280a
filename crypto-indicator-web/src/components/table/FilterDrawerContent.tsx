import React from "react";

import type { FilterConfig, StockFilterConfig } from "../../types/table";

interface FilterDrawerContentProps {
  filterConfig: FilterConfig | StockFilterConfig;
  onUsdSignalChange: (signal: FilterConfig["usdSignal"] | StockFilterConfig["signal"]) => void;
  onBtcSignalChange: (signal: FilterConfig["btcSignal"]) => void;
  assetType?: 'crypto' | 'stock';
}

export const FilterDrawerContent: React.FC<FilterDrawerContentProps> = ({
  filterConfig,
  onUsdSignalChange,
  onBtcSignalChange,
  assetType = 'crypto',
}) => {
  const signalLabel = assetType === 'crypto' ? 'USD Signal' : 'Signal';
  const signalValue = assetType === 'crypto'
    ? (filterConfig as FilterConfig).usdSignal
    : (filterConfig as StockFilterConfig).signal;

  return (
    <div className="filter-drawer-content">
      <div className="filter-section">
        <label htmlFor="mobile-signal">{signalLabel}</label>
        <select
          id="mobile-signal"
          value={signalValue}
          onChange={(e) => {
            const value = e.target.value as 'all' | 'gold' | 'blue' | 'gray';
            onUsdSignalChange(value);
          }}
          className="filter-drawer-select"
        >
          <option value="all">All Signals</option>
          <option value="gold">🟡 Gold (Bullish)</option>
          <option value="blue">🔵 Blue (Bearish)</option>
          <option value="gray">⚪ Gray (Neutral)</option>
        </select>
      </div>

      <div className="filter-section">
        <label htmlFor="mobile-btc-signal">BTC Signal</label>
        <select
          id="mobile-btc-signal"
          value={assetType === 'crypto' ? (filterConfig as FilterConfig).btcSignal : (filterConfig as StockFilterConfig).btcSignal}
          onChange={(e) => {
            const value = e.target.value as 'all' | 'gold' | 'blue' | 'gray';
            onBtcSignalChange(value);
          }}
          className="filter-drawer-select"
        >
          <option value="all">All Signals</option>
          <option value="gold">🟡 Gold (Bullish)</option>
          <option value="blue">🔵 Blue (Bearish)</option>
          <option value="gray">⚪ Gray (Neutral)</option>
        </select>
      </div>
    </div>
  );
};
