import React from "react";

import { CryptoCard } from "./CryptoCard";
import { TableFilters } from "./TableFilters";

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from "../../generated";
import type { FilterConfig, StockFilterConfig } from "../../types/table";

interface TabletLayoutProps {
  data: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  btcStatistics: CryptoCurrencyStatisticsDto[];
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
  filterConfig: FilterConfig | StockFilterConfig;
  onSymbolSearchChange: (search: string) => void;
  onUsdSignalChange: (signal: FilterConfig["usdSignal"] | StockFilterConfig["signal"]) => void;
  onBtcSignalChange: (signal: FilterConfig["btcSignal"]) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
  assetType?: 'crypto' | 'stock';
}

export const TabletLayout: React.FC<TabletLayoutProps> = ({
  data,
  btcStatistics,
  onSignalClick,
  formatDate,
  findBtcDataForSymbol,
  filterConfig,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
  assetType = 'crypto',
}) => {
  return (
    <div className="tablet-layout">
      <TableFilters
        filterConfig={filterConfig}
        onSymbolSearchChange={onSymbolSearchChange}
        onUsdSignalChange={onUsdSignalChange}
        onBtcSignalChange={onBtcSignalChange}
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
        assetType={assetType}
      />

      <div className="crypto-cards-grid">
        {data.map((item) => {
          const btcData = assetType === 'crypto' ? findBtcDataForSymbol(btcStatistics, item.symbol) : undefined;
          return (
            <CryptoCard
              key={item.symbol}
              crypto={item as CryptoCurrencyStatisticsDto}
              {...(btcData && { btcData })}
              onSignalClick={onSignalClick}
              formatDate={formatDate}
              assetType={assetType}
            />
          );
        })}
      </div>
    </div>
  );
};
