import React from "react";

import { GenericDesktopLayout } from "./GenericDesktopLayout";

import type { GenericTableProps } from "../../types/genericTable";

interface GenericMobileLayoutProps extends GenericTableProps {
  showFilters: boolean;
  onToggleFilters: () => void;
  onCloseFilters: () => void;
  filteredCount?: number;
  totalCount?: number;
}

// For now, use desktop layout as fallback
// TODO: Implement proper mobile card layout
export const GenericMobileLayout: React.FC<GenericMobileLayoutProps> = (props) => {
  return <GenericDesktopLayout {...props} />;
};
