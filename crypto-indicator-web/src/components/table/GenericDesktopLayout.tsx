import React from "react";

import { CSS_CLASSES } from "../../constants/app";

import { GenericSortableTableHeader } from "./GenericSortableTableHeader";
import { GenericTableFilters } from "./GenericTableFilters";
import { GenericTableRow } from "./GenericTableRow";

import type { GenericTableProps } from "../../types/genericTable";

interface GenericDesktopLayoutProps extends GenericTableProps {
  showFilters: boolean;
  onToggleFilters: () => void;
  onCloseFilters: () => void;
  filteredCount?: number;
  totalCount?: number;
}

export const GenericDesktopLayout: React.FC<GenericDesktopLayoutProps> = ({
  assetConfig,
  data,
  btcStatistics,
  onSignalClick,
  formatDate,
  findBtcDataForSymbol,
  onSort,
  getSortDirection,
  filterConfig,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount = data.length,
  totalCount = data.length,
}) => {
  return (
    <div className="desktop-layout">
      <GenericTableFilters
        assetConfig={assetConfig}
        filterConfig={filterConfig}
        onSymbolSearchChange={onSymbolSearchChange}
        onUsdSignalChange={onUsdSignalChange}
        onBtcSignalChange={onBtcSignalChange}
        onSignalChange={onSignalChange}
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
      />

      <div className={CSS_CLASSES.TABLE_CONTAINER}>
        <table className={CSS_CLASSES.TABLE}>
          <GenericSortableTableHeader
            assetConfig={assetConfig}
            onSort={onSort}
            getSortDirection={getSortDirection}
          />
          <tbody>
            {data.map((item) => (
              <GenericTableRow
                key={item.symbol}
                assetConfig={assetConfig}
                data={item}
                btcStatistics={btcStatistics}
                onSignalClick={onSignalClick}
                formatDate={formatDate}
                findBtcDataForSymbol={findBtcDataForSymbol}
              />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
