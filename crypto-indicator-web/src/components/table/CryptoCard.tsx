import React, { useState } from "react";

import { navigation } from "../../utils/formatters";

import { CryptoCardDetails } from "./CryptoCardDetails";
import { CryptoCardHeader } from "./CryptoCardHeader";
import { CryptoCardSignals } from "./CryptoCardSignals";

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from "../../generated";

// Component-specific styles
import '../../styles/layout/mobile-components.css';

interface CryptoCardProps {
  crypto: CryptoCurrencyStatisticsDto | StockStatisticsDto;
  btcData?: IndicatorValueDto;
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
  assetType?: 'crypto' | 'stock';
}

// eslint-disable-next-line max-lines-per-function
export const CryptoCard: React.FC<CryptoCardProps> = ({
  crypto,
  btcData,
  onSignalClick,
  formatDate,
  assetType = 'crypto',
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const usdData = crypto.indicatorValues.find(Boolean);

  const usdTooltip = `Click to view chart${
    (usdData?.timestamp !== null && usdData?.timestamp !== undefined) ? ` • Last update: ${formatDate(usdData.timestamp)}` : ""
  }`;
  const btcTooltip = `Click to view chart${
    (btcData?.timestamp !== null && btcData?.timestamp !== undefined) ? ` • Last update: ${formatDate(btcData.timestamp)}` : ""
  }`;

  const handleSymbolClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card expansion
    if (assetType === 'crypto' && 'mapping' in crypto) {
      navigation.openCoinMarketCap(crypto.mapping?.slug);
    }
    // For stocks, we could add navigation to a stock info site
  };

  const handleCardClick = () => {
    setIsExpanded(!isExpanded);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      handleCardClick();
    }
  };

  return (
    <div
      className="crypto-card"
      onClick={handleCardClick}
      onKeyDown={handleKeyDown}
      role="button"
      tabIndex={0}
      aria-expanded={isExpanded}
      aria-label={`${crypto.symbol} cryptocurrency card. Click to ${isExpanded ? "collapse" : "expand"} details.`}
    >
      <CryptoCardHeader
        crypto={crypto}
        usdData={usdData}
        onSymbolClick={handleSymbolClick}
        assetType={assetType}
      />

      <CryptoCardSignals
        symbol={crypto.symbol}
        usdData={usdData}
        btcData={btcData}
        usdTooltip={usdTooltip}
        btcTooltip={btcTooltip}
        onSignalClick={onSignalClick}
        assetType={assetType}
      />

      {isExpanded && (
        <CryptoCardDetails
          usdData={usdData}
          btcData={btcData}
          formatDate={formatDate}
        />
      )}

      <div className="crypto-card-expand-indicator">
        <span className={`expand-icon ${isExpanded ? "expanded" : ""}`}>▼</span>
      </div>
    </div>
  );
};
