import React from "react";

import { CURRENCIES } from "../../constants/app";
import { SignalBadge } from "../signals/SignalBadge";

import type { IndicatorValueDto } from "../../generated";

interface CryptoCardSignalsProps {
  symbol: string;
  usdData: IndicatorValueDto | undefined;
  btcData: IndicatorValueDto | undefined;
  usdTooltip: string;
  btcTooltip: string;
  onSignalClick: (symbol: string, currency: string) => void;
  assetType?: 'crypto' | 'stock';
}

export const CryptoCardSignals: React.FC<CryptoCardSignalsProps> = ({
  symbol,
  usdData,
  btcData,
  usdTooltip,
  btcTooltip,
  onSignalClick,
  assetType = 'crypto',
}) => {
  return (
    <div className="crypto-card-signals">
      <div className="signal-group">
        <span className="signal-label">{assetType === 'crypto' ? 'USD Signal' : 'Signal'}</span>
        <div
          onClick={(e) => { e.stopPropagation(); }}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              e.stopPropagation();
            }
          }}
          role="button"
          tabIndex={0}
        >
          <SignalBadge
            color={usdData?.color}
            onClick={() => { onSignalClick(symbol, assetType === 'crypto' ? CURRENCIES.USD : 'USD'); }}
            clickable
            title={usdTooltip}
          />
        </div>
      </div>
      {assetType === 'crypto' && (
        <div className="signal-group">
          <span className="signal-label">BTC Signal</span>
          <div
            onClick={(e) => { e.stopPropagation(); }}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                e.preventDefault();
                e.stopPropagation();
              }
            }}
            role="button"
            tabIndex={0}
          >
            <SignalBadge
              color={btcData?.color}
              onClick={() => { onSignalClick(symbol, CURRENCIES.BTC); }}
              clickable
              title={btcTooltip}
            />
          </div>
        </div>
      )}
    </div>
  );
};
