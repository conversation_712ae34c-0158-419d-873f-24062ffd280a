import { getLatestStockData } from "./stockDataProcessors";

import type { StockIndicatorValueDto, StockStatisticsDto } from "../generated";
import type { StockSortColumn,StockSortConfig } from "../types/table";

/**
 * Get sort value for a stock based on column
 */
const getSortValue = (
  stock: StockStatisticsDto,
  column: StockSortColumn,
  latestData: StockIndicatorValueDto | undefined,
): string | number => {
  switch (column) {
    case 'symbol': {
      return stock.symbol;
    }
    case 'usdPrice': {
      return latestData?.close ?? 0;
    }
    case 'marketCap': {
      return latestData?.volume ?? 0;
    }
    case 'usdSignal': {
      // Sort by signal color: gold > blue > gray
      const signalOrder = { gold: 3, blue: 2, gray: 1 };
      return signalOrder[latestData?.color as keyof typeof signalOrder] ?? 0;
    }
    case 'btcPrice': {
      // For stocks, BTC price might not be available, return 0
      return 0;
    }
    case 'btcSignal': {
      // For stocks, BTC signal might not be available, return 0
      return 0;
    }
    default: {
      return '';
    }
  }
};

/**
 * Compare two values for sorting
 */
const compareValues = (
  aValue: string | number,
  bValue: string | number,
  direction: 'asc' | 'desc',
): number => {
  if (typeof aValue === 'string' && typeof bValue === 'string') {
    const comparison = aValue.localeCompare(bValue);
    return direction === 'asc' ? comparison : -comparison;
  }

  if (typeof aValue === 'number' && typeof bValue === 'number') {
    const comparison = aValue - bValue;
    return direction === 'asc' ? comparison : -comparison;
  }

  return 0;
};

/**
 * Apply sorting to stock data
 */
export const applyStockSorting = (
  data: StockStatisticsDto[],
  sortConfig: StockSortConfig,
): StockStatisticsDto[] => {
  if (!sortConfig.column || !sortConfig.direction) {
    return data;
  }

  return [...data].sort((a, b) => {
    const aData = getLatestStockData(a);
    const bData = getLatestStockData(b);

    if (!sortConfig.column || !sortConfig.direction) {
      return 0;
    }

    const aValue = getSortValue(a, sortConfig.column, aData);
    const bValue = getSortValue(b, sortConfig.column, bData);

    return compareValues(aValue, bValue, sortConfig.direction);
  });
};
