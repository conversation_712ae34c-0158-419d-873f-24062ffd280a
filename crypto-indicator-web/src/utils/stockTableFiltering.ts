import { getLatestStockData } from "./stockDataProcessors";

import type { StockStatisticsDto } from "../generated";
import type { StockFilterConfig } from "../types/table";

/**
 * Apply filters to stock data
 */
export const applyStockFilters = (
  data: StockStatisticsDto[],
  filterConfig: StockFilterConfig,
): StockStatisticsDto[] => {
  return data.filter((stock) => {
    const latestData = getLatestStockData(stock);

    // Symbol search filter
    if (filterConfig.symbolSearch) {
      const searchTerm = filterConfig.symbolSearch.toLowerCase();
      const symbolMatch = stock.symbol.toLowerCase().includes(searchTerm);
      const nameMatch = stock.mapping?.name?.toLowerCase().includes(searchTerm) ?? false;

      if (!symbolMatch && !nameMatch) {
        return false;
      }
    }

    // Signal filter
    return !(filterConfig.usdSignal !== 'all' && latestData && latestData.color !== filterConfig.usdSignal);
  });
};
