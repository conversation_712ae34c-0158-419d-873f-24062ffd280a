import React, { createContext, ReactNode, useMemo } from 'react';

import { CryptoIndicatorApiClient } from '../generated';
import { CryptoDataService } from '../services/CryptoDataService';

interface ApiContextType {
  cryptoDataService: CryptoDataService;
  apiClient: CryptoIndicatorApiClient;
}

const ApiContext = createContext<ApiContextType | null>(null);

interface ApiProviderProps {
  children: ReactNode;
}

/**
 * API Context Provider
 * Implements dependency injection for services
 */
export const ApiProvider: React.FC<ApiProviderProps> = ({ children }) => {
  const contextValue = useMemo(() => {
    const apiClient = new CryptoIndicatorApiClient();
    const cryptoDataService = new CryptoDataService(apiClient);

    return {
      cryptoDataService,
      apiClient,
    };
  }, []);

  return (
    <ApiContext.Provider value={contextValue}>
      {children}
    </ApiContext.Provider>
  );
};


