import { useCallback, useMemo, useState } from "react";

import type { SignalColor, StockFilterConfig } from "../types/table";

interface UseStockFilteringReturn {
  filterConfig: StockFilterConfig;
  updateSymbolSearch: (search: string) => void;
  updateSignal: (signal: SignalColor) => void;
  clearFilters: () => void;
  hasActiveFilters: boolean;
}

const DEFAULT_FILTER_CONFIG: StockFilterConfig = {
  symbolSearch: '',
  usdSignal: 'all',
  btcSignal: 'all',
};

export const useStockFiltering = (): UseStockFilteringReturn => {
  const [filterConfig, setFilterConfig] = useState<StockFilterConfig>(DEFAULT_FILTER_CONFIG);

  const updateSymbolSearch = useCallback((search: string): void => {
    setFilterConfig(prev => ({ ...prev, symbolSearch: search }));
  }, []);

  const updateSignal = useCallback((signal: SignalColor): void => {
    setFilterConfig(prev => ({ ...prev, signal }));
  }, []);

  const clearFilters = useCallback((): void => {
    setFilterConfig(DEFAULT_FILTER_CONFIG);
  }, []);

  const hasActiveFilters = useMemo((): boolean => {
    return filterConfig.symbolSearch !== '' || filterConfig.usdSignal !== 'all';
  }, [filterConfig]);

  return {
    filterConfig,
    updateSymbolSearch,
    updateSignal,
    clearFilters,
    hasActiveFilters,
  };
};
