import { join } from 'node:path';

import { Mo<PERSON><PERSON> } from '@nestjs/common';
import { ServeStaticModule } from '@nestjs/serve-static';

import FinancialIndicatorRestClient from './api/financial.indicator.api';
import { CryptoStatisticsController } from './crypto.statistics.controller';
import { CryptoStatisticsService } from './crypto.statistics.service';
import { DataTransformationService } from './services/data-transformation.service';
import { StockDataTransformationService } from './services/stock-data-transformation.service';
import { StockStatisticsController } from './stock.statistics.controller';
import { StockStatisticsService } from './stock.statistics.service';

@Module({
  imports: [
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', '..', 'crypto-indicator-web', 'build'),
    }),
  ],
  controllers: [CryptoStatisticsController, StockStatisticsController],
  providers: [
    CryptoStatisticsService,
    StockStatisticsService,
    DataTransformationService,
    StockDataTransformationService,
    {
      provide: 'daemonClient',
      useClass: FinancialIndicatorRestClient,
    },
  ],
})
export class CryptoStatisticsModule {}
